import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { IInvoiceRes } from '@/types';

// Enums for field types and sources
export enum FieldType {
  STRING = "string",
  NUMBER = "number",
  BOOL = "bool",
  DATE = "date",
  SELECT = "select",
  TEXTAREA = "textarea"
}

export enum FieldFormat {
  TEXT = "text",
  NUMBER = "number",
  DATE = "date",
  BOOL = "bool",
  SELECT = "select",
  TEXTAREA = "textarea"
}

export enum FieldSource {
  INVOICE = "Invoice",
  ITEM = "Item",
  FIELDS = "Fields",
  FIXED = "Fixed"
}

interface IInvoiceCustomField {
  description: string;
  name: string;
  path: string;
  type: FieldType.STRING | FieldType.NUMBER | FieldType.BOOL;
  format: FieldFormat.TEXT | FieldFormat.NUMBER | FieldFormat.DATE | FieldFormat.BOOL;
  width: number;
  index: number;
  source: FieldSource;
  value?: string | number | boolean;
}

interface IInvoiceField {
  name: string;
  description: string;
  type: FieldType;
  format: FieldFormat;
  source: FieldSource;
  index: number;
  value?: any;
  options?: { label: string; value: any }[];
  required?: boolean;
}
// Enums for invoice status and payment methods
export enum InvoiceStatus {
  DRAFT = "draft",
  SENT = "sent",
  PAID = "paid",
  OVERDUE = "overdue"
}

export enum PaymentMethod {
  CASH = "cash",
  CHECK = "check",
  CREDIT_CARD = "credit_card",
  BANK_TRANSFER = "bank_transfer",
  OTHER = "other"
}

// Options arrays derived from enums
const INVOICE_STATUS_OPTIONS = [
  { label: "Draft", value: InvoiceStatus.DRAFT },
  { label: "Sent", value: InvoiceStatus.SENT },
  { label: "Paid", value: InvoiceStatus.PAID },
  { label: "Overdue", value: InvoiceStatus.OVERDUE }
];

const PAYMENT_METHOD_OPTIONS = [
  { label: "Cash", value: PaymentMethod.CASH },
  { label: "Check", value: PaymentMethod.CHECK },
  { label: "Credit Card", value: PaymentMethod.CREDIT_CARD },
  { label: "Bank Transfer", value: PaymentMethod.BANK_TRANSFER },
  { label: "Other", value: PaymentMethod.OTHER }
];

function getInvoiceFields(invoice: IInvoiceRes | null): IInvoiceField[] {
  if (!invoice) return [];

  const baseFields: IInvoiceField[] = [
    {
      name: 'number',
      description: 'Invoice Number',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.INVOICE,
      index: 1,
      value: invoice.number || '',
      required: true
    },
    {
      name: 'status',
      description: 'Status',
      type: FieldType.STRING,
      format: FieldFormat.SELECT,
      source: FieldSource.INVOICE,
      index: 2,
      value: invoice.status || InvoiceStatus.DRAFT,
      options: INVOICE_STATUS_OPTIONS,
      required: true
    },
    {
      name: 'paymentMethod',
      description: 'Payment Method',
      type: FieldType.STRING,
      format: FieldFormat.SELECT,
      source: FieldSource.INVOICE,
      index: 3,
      value: invoice.paymentMethod || '',
      options: PAYMENT_METHOD_OPTIONS
    },
    {
      name: 'dueOn',
      description: 'Due Date',
      type: FieldType.DATE,
      format: FieldFormat.DATE,
      source: FieldSource.INVOICE,
      index: 4,
      value: invoice.dueOn ? new Date(invoice.dueOn).toISOString().split('T')[0] : '',
      required: true
    },
    {
      name: 'notes',
      description: 'Notes',
      type: FieldType.STRING,
      format: FieldFormat.TEXTAREA,
      source: FieldSource.INVOICE,
      index: 5,
      value: invoice.notes || ''
    },
    {
      name: 'email',
      description: 'Email',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.INVOICE,
      index: 6,
      value: invoice.email || ''
    },
    {
      name: 'phone',
      description: 'Phone',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.INVOICE,
      index: 7,
      value: invoice.phone || ''
    },
    {
      name: 'address',
      description: 'Address',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.INVOICE,
      index: 8,
      value: invoice.address || ''
    },
    {
      name: 'city',
      description: 'City',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.INVOICE,
      index: 9,
      value: invoice.city || ''
    },
    {
      name: 'province',
      description: 'Province/State',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.INVOICE,
      index: 10,
      value: invoice.province || ''
    },
    {
      name: 'country',
      description: 'Country',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.INVOICE,
      index: 11,
      value: invoice.country || ''
    },
    {
      name: 'subTotal',
      description: 'Subtotal',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.FIXED,
      index: 20,
      value: invoice.subTotal || 0
    },
    {
      name: 'taxTotal',
      description: 'Tax Total',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.FIXED,
      index: 21,
      value: invoice.tax || 0
    },
    {
      name: 'total',
      description: 'Total',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.FIXED,
      index: 22,
      value: invoice.total || 0
    }
  ];

  const getCustomFields = (): IInvoiceCustomField[] => {
    const invoiceCustomFields = (invoice as any).customFields;

    if (invoiceCustomFields && Array.isArray(invoiceCustomFields)) {
      return invoiceCustomFields.map((field: any) => ({
        description: field.description || field.name,
        name: field.name,
        path: field.path || '',
        type: field.type || 'string',
        format: field.format || 'text',
        width: field.width || 30,
        index: field.index || 10,
        source: field.source || 'Fields',
        value: field.value || ''
      }));
    }
    // Fallback to Mock custom fields when API doesn't provide them
    return[]
    return [
      {
        description: "Labor",
        name: "TotalPrice",
        path: "I",
        type: FieldType.STRING,
        format: FieldFormat.TEXT,
        width: 30,
        index: 15,
        source: FieldSource.ITEM,
        value: invoiceCustomFields?.TotalPrice || ''
      },
      {
        description: "Overtime",
        name: "OvertimeRate",
        path: "J",
        type: FieldType.STRING,
        format: FieldFormat.TEXT,
        width: 30,
        index: 16,
        source: FieldSource.ITEM,
        value: invoiceCustomFields?.OvertimeRate || ''
      },
      {
        description: "Project Code",
        name: "ProjectCode",
        path: "K",
        type: FieldType.STRING,
        format: FieldFormat.TEXT,
        width: 25,
        index: 12,
        source: FieldSource.FIELDS,
        value: invoiceCustomFields?.ProjectCode || ''
      },
      {
        description: "Priority Level",
        name: "Priority",
        path: "L",
        type: FieldType.STRING,
        format: FieldFormat.TEXT,
        width: 20,
        index: 13,
        source: FieldSource.FIELDS,
        value: invoiceCustomFields?.Priority || ''
      },
      {
        description: "Requires Approval",
        name: "RequiresApproval",
        path: "M",
        type: FieldType.BOOL,
        format: FieldFormat.BOOL,
        width: 15,
        index: 14,
        source: FieldSource.FIELDS,
        value: invoiceCustomFields?.RequiresApproval || false
      }
    ];
  };

  const customFields = getCustomFields();

  const convertedCustomFields: IInvoiceField[] = customFields.map(field => ({
    name: field.name,
    description: field.description,
    type: field.type,
    format: field.format,
    source: field.source,
    index: field.index,
    value: field.value
  }));

  const allFields = [...baseFields, ...convertedCustomFields];
  return allFields.sort((a, b) => a.index - b.index);
}

interface EditInvoiceDialogProps {
  isOpen: boolean;
  onClose: (open: boolean) => void;
  invoice: IInvoiceRes | null;
  onSave: (updatedInvoice: Partial<IInvoiceRes>) => void;
}

export function EditInvoiceDialog({
  isOpen,
  onClose,
  invoice,
  onSave
}: EditInvoiceDialogProps) {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [invoiceFields, setInvoiceFields] = useState<IInvoiceField[]>([]);

  useEffect(() => {
    if (invoice) {
      const fields = getInvoiceFields(invoice);
      setInvoiceFields(fields);

      // Initialize form data with current invoice values
      const initialData: Record<string, any> = {};
      fields.forEach(field => {
        initialData[field.name] = field.value;
      });
      setFormData(initialData);
    }
  }, [invoice]);

  if (!invoice) return null;

  const groupedFields = invoiceFields.reduce((groups, field) => {
    if (!groups[field.source]) {
      groups[field.source] = [];
    }
    groups[field.source].push(field);
    return groups;
  }, {} as Record<string, IInvoiceField[]>);

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const renderField = (field: IInvoiceField) => {
    const fieldId = `field_${field.name}`;
    const value = formData[field.name] || '';

    switch (field.format) {
      case FieldFormat.SELECT:
        return (
          <Select
            value={value}
            onValueChange={(selectedValue) => handleFieldChange(field.name, selectedValue)}
          >
            <SelectTrigger id={fieldId}>
              <SelectValue placeholder={`Select ${field.description.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case FieldFormat.TEXTAREA:
        return (
          <Textarea
            id={fieldId}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={`Enter ${field.description.toLowerCase()}`}
            rows={3}
          />
        );

      case FieldFormat.DATE:
        return (
          <Input
            id={fieldId}
            type="date"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
          />
        );

      case FieldFormat.NUMBER:
        return (
          <Input
            id={fieldId}
            type="number"
            step="0.01"
            value={value}
            onChange={(e) => handleFieldChange(field.name, parseFloat(e.target.value) || 0)}
            placeholder="0.00"
          />
        );

      case FieldFormat.BOOL:
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={fieldId}
              checked={value === true || value === 'true'}
              onCheckedChange={(checked) => handleFieldChange(field.name, checked)}
            />
            <Label htmlFor={fieldId} className="text-sm font-normal">
              {field.description}
            </Label>
          </div>
        );

      case FieldFormat.TEXT:
      default:
        return (
          <Input
            id={fieldId}
            type="text"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={`Enter ${field.description.toLowerCase()}`}
          />
        );
    }
  };

  const handleSave = () => {
    const customFieldValues: Record<string, any> = {};
    invoiceFields
      .filter(field => field.source === FieldSource.FIELDS || field.source === FieldSource.ITEM)
      .forEach((field: IInvoiceField) => {
        if (formData[field.name] !== undefined) {
          customFieldValues[field.name] = formData[field.name];
        }
      });
    const updatedInvoice: any = {
      ...formData,
      dueOn: formData.dueOn ? new Date(formData.dueOn).toISOString() : invoice.dueOn,
      customFields: Object.keys(customFieldValues).length > 0 ? customFieldValues : undefined
    };
    onSave(updatedInvoice);
    onClose(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Invoice #{invoice.number || invoice.id}</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <Accordion type="multiple" defaultValue={["Invoice", "Fields", "Item", "Fixed"]} className="w-full">
            {/* Invoice Section */}
            {groupedFields.Invoice && (
              <AccordionItem value="Invoice">
                <AccordionTrigger className="text-lg font-semibold">
                  Invoice Information
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid gap-4 py-4">
                    {groupedFields.Invoice.map((field) => (
                      <div key={field.name} className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor={`field_${field.name}`} className="text-right">
                          {field.description}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <div className="col-span-3">
                          {renderField(field)}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* Fields Section (Custom Fields) */}
            {groupedFields.Fields && (
              <AccordionItem value="Fields">
                <AccordionTrigger className="text-lg font-semibold">
                  Custom Fields
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid gap-4 py-4">
                    {groupedFields.Fields.map((field) => (
                      <div key={field.name} className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor={`field_${field.name}`} className="text-right">
                          {field.description}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <div className="col-span-3">
                          {renderField(field)}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* Item Section */}
            {groupedFields.Item && (
              <AccordionItem value="Item">
                <AccordionTrigger className="text-lg font-semibold">
                  Item Information
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid gap-4 py-4">
                    {groupedFields.Item.map((field) => (
                      <div key={field.name} className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor={`field_${field.name}`} className="text-right">
                          {field.description}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <div className="col-span-3">
                          {renderField(field)}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* Fixed Section (Editable totals) */}
            {groupedFields.Fixed && (
              <AccordionItem value="Fixed">
                <AccordionTrigger className="text-lg font-semibold">
                  Invoice Totals
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid gap-4 py-4">
                    {groupedFields.Fixed.map((field) => (
                      <div key={field.name} className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor={`field_${field.name}`} className="text-right font-medium">
                          {field.description}
                        </Label>
                        <div className="col-span-3">
                          {renderField(field)}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}
          </Accordion>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onClose(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}